/**
 * FootFit AI Service Types
 * 
 * Consolidated type definitions for all AI and measurement services
 */

export interface FootMeasurement {
  foot_length: number;
  foot_width: number;
  recommended_size_uk: string;
  recommended_size_us: string;
  recommended_size_eu: string;
  confidence: number;
  recommendations: ShoeRecommendation[];
}

export interface ShoeRecommendation {
  brand: string;
  model: string;
  size_uk: string;
  size_us: string;
  size_eu: string;
  confidence: number;
  fit_type: 'narrow' | 'regular' | 'wide';
  category: string;
  image_url?: string;
}

export interface MeasurementRequest {
  image_url: string;
  user_preferences?: {
    preferred_brands?: string[];
    preferred_categories?: string[];
    preferred_fit?: 'narrow' | 'regular' | 'wide';
  };
}

export interface MeasurementResponse {
  success: boolean;
  data?: FootMeasurement;
  error?: string;
  processing_time_ms: number;
}
