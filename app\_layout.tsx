import { DarkTheme, DefaultTheme, ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import 'react-native-reanimated';

import { ErrorBoundary } from '@/components/ErrorBoundary';
import { AuthProvider } from '@/contexts/AuthContext';
import { ThemeProvider, useTheme } from '@/contexts/ThemeContext';
import { hideSplashScreen, initializeSplashScreen } from '@/lib/splashScreen';

function AppContent() {
  const { colorScheme, colors } = useTheme();

  // Initialize environment validation and TensorFlow.js
  useEffect(() => {
    const initializeApp = async () => {
      try {
        const { env } = await import('@/utils/env');
        const { log } = await import('@/utils/logger');

        // Validate environment configuration
        const config = env.validateAndGetConfig();
        log.info('App initialized successfully', 'App', {
          environment: config.isDevelopment ? 'development' : 'production',
          version: config.appVersion,
        });

        // Initialize Real CNN Foot Analysis Service for AI processing
        log.info('Initializing Real CNN Foot Analysis Service...', 'App');
        const { RealCNNFootAnalysisService } = await import('@/services/realCNNFootAnalysis');
        const cnnInitialized = await RealCNNFootAnalysisService.initialize();

        if (cnnInitialized) {
          log.info('Real CNN Foot Analysis Service initialized successfully', 'App');
        } else {
          log.warn('Real CNN Foot Analysis Service initialization failed, will use fallback', 'App');
        }

        // Initialize Real CNN Service for foot measurement
        log.info('Initializing Real CNN Service...', 'App');
        const { RealCNNService } = await import('@/services/realCNNService');
        const aiInitialized = await RealCNNService.initialize();

        if (aiInitialized) {
          log.info('Real CNN Service initialized successfully', 'App');
          // Test the service
          const testPassed = await RealCNNService.testService();
          if (testPassed) {
            log.info('Real CNN Service test passed', 'App');
          } else {
            log.warn('Real CNN Service test failed, but service is available', 'App');
          }
        } else {
          log.warn('Real CNN Service initialization failed, will use fallback', 'App');
        }

      } catch (error) {
        const { log } = await import('@/utils/logger');
        log.error('App initialization failed', 'App', error);
        // In production, this should show a user-friendly error screen
        if (process.env.NODE_ENV === 'production') {
          // Could show an error boundary or maintenance screen
        }
      }
    };

    initializeApp();
  }, []);

  // Handle authentication redirects
  const { useAuthRedirect } = require('@/hooks/useAuthRedirect');
  useAuthRedirect();



  // Create custom navigation theme based on our colors
  const navigationTheme = {
    ...(colorScheme === 'dark' ? DarkTheme : DefaultTheme),
    colors: {
      ...(colorScheme === 'dark' ? DarkTheme.colors : DefaultTheme.colors),
      primary: colors.primary,
      background: colors.background,
      card: colors.background,
      text: colors.text,
      border: colors.border,
      notification: colors.primary,
    },
  };

  return (
    <NavigationThemeProvider value={navigationTheme}>
      <Stack>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="upload" options={{ headerShown: false }} />
        <Stack.Screen name="camera" options={{ headerShown: false }} />
        <Stack.Screen name="processing" options={{ headerShown: false }} />
        <Stack.Screen name="results" options={{ headerShown: false }} />
        <Stack.Screen name="profile/edit" options={{ headerShown: false }} />
        <Stack.Screen name="auth/login" options={{ headerShown: false }} />
        <Stack.Screen name="auth/signup" options={{ headerShown: false }} />
        <Stack.Screen name="auth/forgot-password" options={{ headerShown: false }} />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />


    </NavigationThemeProvider>
  );
}

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  // Initialize splash screen and services
  useEffect(() => {
    const initializeApp = async () => {
      // Initialize splash screen first
      await initializeSplashScreen();

      // App initialization complete
    };

    initializeApp();
  }, []);

  // Hide splash screen when fonts are loaded
  useEffect(() => {
    if (loaded) {
      hideSplashScreen();
    }
  }, [loaded]);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ThemeProvider>
      <ErrorBoundary>
        <AuthProvider>
          <AppContent />
        </AuthProvider>
      </ErrorBoundary>
    </ThemeProvider>
  );
}
