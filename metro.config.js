const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add support for TensorFlow.js and resolve bundling issues
config.resolver.alias = {
  ...config.resolver.alias,
  // Resolve TensorFlow.js platform issues
  '@tensorflow/tfjs-platform-react-native': path.resolve(
    __dirname,
    'node_modules/@tensorflow/tfjs-react-native/dist/platform_react_native.js'
  ),
};

// Add file extensions for TensorFlow.js
config.resolver.sourceExts = [
  ...config.resolver.sourceExts,
  'bin', // For TensorFlow.js model weights
];

// Configure asset extensions for model files
config.resolver.assetExts = [
  ...config.resolver.assetExts,
  'bin', // TensorFlow.js model weights
  'json', // TensorFlow.js model architecture
  'tflite', // TensorFlow Lite models
];

// Configure transformer to handle TensorFlow.js modules
config.transformer = {
  ...config.transformer,
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

// Resolve module resolution issues
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Configure Metro to handle large bundles (for TensorFlow.js)
config.serializer = {
  ...config.serializer,
  customSerializer: undefined,
};

// Add specific resolver for problematic modules
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // Handle TensorFlow.js platform resolution
  if (moduleName === '@tensorflow/tfjs-platform-react-native') {
    return {
      filePath: path.resolve(
        __dirname,
        'node_modules/@tensorflow/tfjs-react-native/dist/platform_react_native.js'
      ),
      type: 'sourceFile',
    };
  }

  // Handle TensorFlow.js core resolution
  if (moduleName === '@tensorflow/tfjs-react-native') {
    return {
      filePath: path.resolve(
        __dirname,
        'node_modules/@tensorflow/tfjs-react-native/dist/index.js'
      ),
      type: 'sourceFile',
    };
  }

  // Default resolution
  return context.resolveRequest(context, moduleName, platform);
};

// Configure watchman to ignore large directories
config.watchFolders = [
  path.resolve(__dirname, 'node_modules'),
  path.resolve(__dirname, 'assets'),
  path.resolve(__dirname, 'datasets'),
];

// Ignore problematic files that cause bundling issues
config.resolver.blacklistRE = /node_modules\/.*\/node_modules\/react-native\/.*/;

module.exports = config;
