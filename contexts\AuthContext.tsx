import { supabase } from '@/lib/supabase';
import { AuthError, Session, User } from '@supabase/supabase-js';
import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';

interface Profile {
  id: string;
  email: string;
  full_name: string | null;
  avatar_url: string | null;
  preferred_brands: string[] | null;
  preferred_unit: 'cm' | 'inches';
  created_at: string;
  updated_at: string;
}

interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  signUp: (email: string, password: string, fullName?: string) => Promise<{ error: AuthError | null }>;
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<{ error: AuthError | null }>;
  updateProfile: (updates: Partial<Profile>) => Promise<{ error: Error | null }>;
  refreshProfile: () => Promise<void>;
}

// Create a default context value to prevent undefined errors
const defaultAuthContext: AuthContextType = {
  session: null,
  user: null,
  profile: null,
  loading: true,
  signUp: async () => ({ error: null }),
  signIn: async () => ({ error: null }),
  signOut: async () => ({ error: null }),
  updateProfile: async () => ({ error: new Error('AuthProvider not initialized') }),
  refreshProfile: async () => {},
};

const AuthContext = createContext<AuthContextType>(defaultAuthContext);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      if (session?.user) {
        fetchProfile(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      const { log } = await import('@/utils/logger');
      log.debug(`Auth state changed: ${event}`, 'AuthContext', { userEmail: session?.user?.email });

      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        await fetchProfile(session.user.id);
      } else {
        setProfile(null);
        setLoading(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const fetchProfile = useCallback(async (userId: string) => {
    try {
      setLoading(true);
      const { log } = await import('@/utils/logger');
      log.info('Fetching user profile', 'AuthContext', { userId });

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        log.error('Error fetching profile from Supabase', 'AuthContext', {
          error: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint,
          userId
        });

        // If profile doesn't exist, create a basic one
        if (error.code === 'PGRST116') { // No rows returned
          log.info('Profile not found, creating basic profile', 'AuthContext', { userId });
          setProfile({
            id: userId,
            email: user?.email || '',
            full_name: user?.user_metadata?.full_name || null,
            avatar_url: null,
            preferred_brands: null,
            preferred_unit: 'cm',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });
        } else {
          // For other errors, set profile to null but don't block the app
          setProfile(null);
        }
        return;
      }

      if (data) {
        log.info('Successfully fetched user profile', 'AuthContext', { userId });
        setProfile(data);
      } else {
        log.warn('No profile data returned', 'AuthContext', { userId });
        setProfile(null);
      }
    } catch (error) {
      const { log } = await import('@/utils/logger');
      log.error('Unexpected error fetching profile', 'AuthContext', error);
      setProfile(null);
    } finally {
      setLoading(false);
    }
  }, [user]);

  const signUp = async (email: string, password: string, fullName?: string) => {
    try {
      setLoading(true);


      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });

      if (error) {
        return { error };
      }
      // Profile will be created automatically via database trigger
      return { error: null };
    } catch (error) {
      return { error: error as AuthError };
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);


      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      // Error handling is done by the auth state listener

      return { error };
    } catch (error) {
      return { error: error as AuthError };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      return { error };
    } catch (error) {
      return { error: error as AuthError };
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    try {
      if (!user) {
        return { error: new Error('No user logged in') };
      }

      const { error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (error) {
        return { error: new Error(error.message) };
      }

      // Refresh profile data
      await fetchProfile(user.id);
      return { error: null };
    } catch (error) {
      return { error: error as Error };
    }
  };

  const refreshProfile = async () => {
    if (user) {
      await fetchProfile(user.id);
    }
  };

  const value: AuthContextType = {
    session,
    user,
    profile,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
    refreshProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  return context;
}

// Hook for protected routes
export function useRequireAuth() {
  const { user, loading } = useAuth();
  
  useEffect(() => {
    if (!loading && !user) {
      // Redirect to login screen
      // This will be handled by the navigation logic
    }
  }, [user, loading]);

  return { user, loading };
}
