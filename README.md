# FootFit - AI-Powered Foot Measurement App

A React Native mobile application that uses AI to measure feet from smartphone camera images and provides personalized shoe size recommendations.

## 🎯 Project Overview

FootFit is an academic project that demonstrates the integration of AI/ML technologies with mobile app development. The application allows users to take photos of their feet and receive accurate measurements and shoe recommendations using computer vision and machine learning.

### Key Features

- **AI Foot Measurement**: Real-time foot analysis using computer vision algorithms
- **Multi-Size Support**: Provides UK, US, and EU shoe size recommendations
- **Personalized Recommendations**: Smart shoe suggestions based on foot measurements and user preferences
- **History Tracking**: Saves measurement history with Supabase backend
- **Modern UI/UX**: Clean, responsive design with light/dark theme support
- **Offline Capability**: Enhanced offline AI processing for reliable performance

### Technology Stack

- **Frontend**: React Native with Expo
- **Navigation**: Expo Router with tab-based navigation
- **Backend**: Supabase (Authentication, Database, Storage)
- **AI/ML**: Custom computer vision algorithms for foot measurement
- **State Management**: React Context API
- **Styling**: React Native StyleSheet with theme support

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- iOS Simulator or Android Emulator (or physical device with Expo Go)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd footfitappv3
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npx expo start
   ```

4. **Run on device/simulator**
   - Scan QR code with Expo Go app (iOS/Android)
   - Press `i` for iOS Simulator
   - Press `a` for Android Emulator

## 📱 App Features

### Core Functionality

1. **User Authentication**
   - Email/password registration and login
   - Secure session management with Supabase

2. **Foot Measurement**
   - Camera integration for foot photography
   - AI-powered measurement analysis
   - Real-time processing with visual feedback

3. **Shoe Recommendations**
   - Personalized suggestions based on measurements
   - Category-based filtering (Sports, Casual, Formal, etc.)
   - Brand preference integration

4. **History & Profile**
   - Measurement history tracking
   - User profile management
   - Preference settings

### Navigation Structure

- **Home**: Main dashboard with quick actions
- **Camera**: Direct camera access for foot photography
- **History**: Past measurements and recommendations
- **Account**: Profile settings and preferences
- **Tutorial**: Comprehensive user guidance

## 🏗️ Project Structure

```
footfitappv3/
├── app/                    # Main application screens
│   ├── (tabs)/            # Tab-based navigation screens
│   ├── auth/              # Authentication screens
│   └── profile/           # Profile management screens
├── components/            # Reusable UI components
│   ├── ui/               # Core UI components
│   ├── onboarding/       # Onboarding flow components
│   └── tutorials/        # Tutorial components
├── services/             # Business logic and API services
├── contexts/             # React Context providers
├── constants/            # App constants and configuration
├── hooks/               # Custom React hooks
├── utils/               # Utility functions
├── assets/              # Images, fonts, and static assets
└── supabase/            # Database migrations and setup
```

## 🔧 Configuration

### Environment Setup

The app uses Supabase for backend services. Configuration is handled through:
- `lib/supabase.ts` - Supabase client configuration
- `utils/env.ts` - Environment variable management

### Key Services

- **FootFit AI Analysis** (`services/footAnalysisAI.ts`): Consolidated TensorFlow.js-based foot measurement using real CNN
- **Supabase Service** (`services/supabaseService.ts`): Database operations with guest mode support
- **Shoe Data Service** (`services/shoeDataService.ts`): Comprehensive shoe database operations

## 🎨 Design System

### Theme Support
- Light and dark mode compatibility
- Consistent color palette with emerald green accent (#00C851)
- Typography system with multiple font weights
- Responsive design for various screen sizes

### UI Components
- Custom button components with haptic feedback
- Card-based layouts for content organization
- Loading states and error handling
- Accessible design patterns

## 📊 Academic Significance

This project demonstrates proficiency in:

1. **Mobile App Development**: React Native, Expo, and cross-platform development
2. **AI/ML Integration**: Computer vision algorithms and image processing
3. **Backend Integration**: RESTful APIs, database design, and cloud services
4. **User Experience Design**: Modern UI/UX principles and accessibility
5. **Software Engineering**: Clean code architecture, component reusability, and maintainability

## 🧪 Testing

The application includes comprehensive testing for:
- Core measurement algorithms
- User authentication flows
- Image processing capabilities
- Database operations
- UI component functionality

## 📈 Performance Optimization

- Image compression and optimization
- Offline-first architecture for reliability
- Efficient state management
- Optimized rendering with React Native best practices

## 🔒 Security & Privacy

- Secure authentication with Supabase
- Row-level security (RLS) for data protection
- Image storage with controlled access
- Privacy-focused data handling

## 📝 License

This project is developed for academic purposes as part of a final year project.

---

**Developed by**: [Student Name]
**Academic Institution**: [University Name]
**Project Year**: 2024
**Supervisor**: [Supervisor Name]
