/**
 * Comprehensive Verification Script for FootFit Academic Demonstration
 * Validates real CNN implementation and Supabase integration
 */

const fs = require('fs');
const path = require('path');

class FootFitVerifier {
  constructor() {
    this.verificationResults = {
      cnnIntegration: { status: 'PENDING', issues: [], details: {} },
      supabaseIntegration: { status: 'PENDING', issues: [], details: {} },
      processingPipeline: { status: 'PENDING', issues: [], details: {} },
      endToEndFlow: { status: 'PENDING', issues: [], details: {} },
      academicReadiness: { status: 'PENDING', issues: [], details: {} },
      overall: { status: 'PENDING', score: 0, readyForDemo: false }
    };
  }

  /**
   * Run comprehensive verification
   */
  async verify() {
    console.log('🔍 FOOTFIT ACADEMIC DEMONSTRATION VERIFICATION');
    console.log('=' .repeat(60));
    console.log('Verifying Real CNN Implementation & Supabase Integration\n');

    try {
      await this.verifyCNNIntegration();
      await this.verifySupabaseIntegration();
      await this.verifyProcessingPipeline();
      await this.verifyEndToEndFlow();
      await this.verifyAcademicReadiness();
      
      this.calculateOverallStatus();
      this.printComprehensiveResults();

      return this.verificationResults;

    } catch (error) {
      console.error('❌ Verification failed:', error.message);
      throw error;
    }
  }

  /**
   * 1. CNN Integration Verification
   */
  async verifyCNNIntegration() {
    console.log('🧠 1. VERIFYING CNN INTEGRATION...');
    
    const issues = [];
    const details = {};

    // Check RealCNNService exists and structure
    const cnnServicePath = path.join(__dirname, '..', 'services', 'realCNNService.ts');
    if (!fs.existsSync(cnnServicePath)) {
      issues.push('RealCNNService file not found');
    } else {
      const serviceContent = fs.readFileSync(cnnServicePath, 'utf8');
      
      // Verify key components
      details.hasRealCNNService = serviceContent.includes('export class RealCNNService');
      details.hasTensorFlowImport = serviceContent.includes("import('@tensorflow/tfjs')") ||
                                   serviceContent.includes("import * as tf from '@tensorflow/tfjs'") ||
                                   serviceContent.includes('initializeTensorFlow');
      details.hasImageProcessor = serviceContent.includes('class CNNImageProcessor');
      details.hasRealAnalyzer = serviceContent.includes('class RealCNNAnalyzer');
      details.hasSupabaseIntegration = serviceContent.includes('CachedSupabaseService');
      details.hasDynamicLoading = serviceContent.includes('initializeTensorFlow') &&
                                 serviceContent.includes('tensorFlowAvailable');

      if (!details.hasRealCNNService) issues.push('RealCNNService class not found');
      if (!details.hasTensorFlowImport) issues.push('TensorFlow.js import missing');
      if (!details.hasImageProcessor) issues.push('CNNImageProcessor class missing');
      if (!details.hasRealAnalyzer) issues.push('RealCNNAnalyzer class missing');
      if (!details.hasSupabaseIntegration) issues.push('Supabase integration missing in CNN service');
      if (!details.hasDynamicLoading) issues.push('Dynamic TensorFlow.js loading not implemented');
    }

    // Check model files with data-informed weights
    const modelJsonPath = path.join(__dirname, '..', 'assets', 'models', 'foot_measurement_model.json');
    const weightsPath = path.join(__dirname, '..', 'assets', 'models', 'foot_measurement_model_weights.bin');
    
    details.modelJsonExists = fs.existsSync(modelJsonPath);
    details.weightsExist = fs.existsSync(weightsPath);
    
    if (!details.modelJsonExists) issues.push('Model JSON file missing');
    if (!details.weightsExist) issues.push('Model weights file missing');

    // Verify model contains real training data
    if (details.modelJsonExists) {
      try {
        const modelJson = JSON.parse(fs.readFileSync(modelJsonPath, 'utf8'));
        details.hasTrainingInfo = !!modelJson.training_info;
        details.datasetSize = modelJson.training_info?.dataset_size || 0;
        details.isDataInformed = modelJson.training_info?.model_type === 'data_informed_weights';
        details.hasDatasetStats = !!(modelJson.training_info?.dataset_statistics);
        
        if (!details.hasTrainingInfo) issues.push('Model missing training information');
        if (details.datasetSize !== 1629) issues.push(`Expected 1629 images, found ${details.datasetSize}`);
        if (!details.isDataInformed) issues.push('Model not marked as data-informed');
        if (!details.hasDatasetStats) issues.push('Model missing dataset statistics');
        
        // Verify dataset statistics match expected values
        if (details.hasDatasetStats) {
          const stats = modelJson.training_info.dataset_statistics;
          details.footLengthMean = stats.foot_length_mean;
          details.footWidthMean = stats.foot_width_mean;
          
          if (Math.abs(stats.foot_length_mean - 27.53) > 0.1) {
            issues.push(`Unexpected foot length mean: ${stats.foot_length_mean}`);
          }
          if (Math.abs(stats.foot_width_mean - 10.0) > 0.1) {
            issues.push(`Unexpected foot width mean: ${stats.foot_width_mean}`);
          }
        }
      } catch (error) {
        issues.push('Invalid model JSON structure');
      }
    }

    // Check weights file size
    if (details.weightsExist) {
      const weightsStats = fs.statSync(weightsPath);
      details.weightsSize = weightsStats.size;
      details.expectedWeightsSize = 441104; // Expected size for data-informed weights
      
      if (Math.abs(details.weightsSize - details.expectedWeightsSize) > 1000) {
        issues.push(`Weights file size mismatch: ${details.weightsSize} vs ${details.expectedWeightsSize}`);
      }
    }

    // Check TensorFlow.js dependencies
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      details.hasTensorFlowDeps = !!(packageJson.dependencies['@tensorflow/tfjs'] && 
                                     packageJson.dependencies['@tensorflow/tfjs-react-native']);
      
      if (!details.hasTensorFlowDeps) issues.push('TensorFlow.js dependencies missing');
    }

    this.verificationResults.cnnIntegration = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };

    console.log(`   Status: ${this.verificationResults.cnnIntegration.status}`);
    if (issues.length > 0) {
      issues.forEach(issue => console.log(`   ❌ ${issue}`));
    } else {
      console.log('   ✅ Real CNN with data-informed weights verified');
    }
  }

  /**
   * 2. Supabase Integration Verification
   */
  async verifySupabaseIntegration() {
    console.log('\n🗄️  2. VERIFYING SUPABASE INTEGRATION...');
    
    const issues = [];
    const details = {};

    // Check CachedSupabaseService
    const cachedServicePath = path.join(__dirname, '..', 'services', 'cachedSupabaseService.ts');
    if (!fs.existsSync(cachedServicePath)) {
      issues.push('CachedSupabaseService not found');
    } else {
      const serviceContent = fs.readFileSync(cachedServicePath, 'utf8');
      details.hasCachedService = serviceContent.includes('export class CachedSupabaseService');
      details.hasRecommendations = serviceContent.includes('getRecommendations');
      
      if (!details.hasCachedService) issues.push('CachedSupabaseService class missing');
      if (!details.hasRecommendations) issues.push('getRecommendations method missing');
    }

    // Verify aiService.ts has NO static database
    const aiServicePath = path.join(__dirname, '..', 'services', 'aiService.ts');
    if (fs.existsSync(aiServicePath)) {
      const aiServiceContent = fs.readFileSync(aiServicePath, 'utf8');
      
      details.hasStaticDatabase = aiServiceContent.includes('SHOE_DATABASE');
      details.usesSupabaseOnly = aiServiceContent.includes('CachedSupabaseService');
      details.noMockFallback = !aiServiceContent.includes('shoeModels = SHOE_DATABASE');
      
      if (details.hasStaticDatabase) issues.push('Static SHOE_DATABASE still present in aiService.ts');
      if (!details.usesSupabaseOnly) issues.push('aiService.ts not using Supabase integration');
      if (!details.noMockFallback) issues.push('Static database fallback still exists');
    }

    // Check RealCNNService uses Supabase
    const cnnServicePath = path.join(__dirname, '..', 'services', 'realCNNService.ts');
    if (fs.existsSync(cnnServicePath)) {
      const cnnContent = fs.readFileSync(cnnServicePath, 'utf8');
      details.cnnUsesSupabase = cnnContent.includes('CachedSupabaseService.getRecommendations');
      
      if (!details.cnnUsesSupabase) issues.push('RealCNNService not using Supabase for recommendations');
    }

    // Verify no mock services are imported in processing
    const processingPath = path.join(__dirname, '..', 'app', 'processing.tsx');
    if (fs.existsSync(processingPath)) {
      const processingContent = fs.readFileSync(processingPath, 'utf8');
      details.noMockImports = !processingContent.includes('mockAI') && 
                             !processingContent.includes('MockAIService');
      
      if (!details.noMockImports) issues.push('Mock AI services still imported in processing');
    }

    this.verificationResults.supabaseIntegration = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };

    console.log(`   Status: ${this.verificationResults.supabaseIntegration.status}`);
    if (issues.length > 0) {
      issues.forEach(issue => console.log(`   ❌ ${issue}`));
    } else {
      console.log('   ✅ Pure Supabase integration verified (no static fallbacks)');
    }
  }

  /**
   * 3. Processing Pipeline Verification
   */
  async verifyProcessingPipeline() {
    console.log('\n⚙️  3. VERIFYING PROCESSING PIPELINE...');
    
    const issues = [];
    const details = {};

    // Check processing.tsx uses RealCNNService
    const processingPath = path.join(__dirname, '..', 'app', 'processing.tsx');
    if (!fs.existsSync(processingPath)) {
      issues.push('processing.tsx not found');
    } else {
      const processingContent = fs.readFileSync(processingPath, 'utf8');
      
      details.usesRealCNN = processingContent.includes('RealCNNService');
      details.importsRealCNN = processingContent.includes("import('@/services/realCNNService')");
      details.noEnhancedOffline = !processingContent.includes('EnhancedOfflineAIService');
      details.noMockAI = !processingContent.includes('MockAIService');
      
      if (!details.usesRealCNN) issues.push('processing.tsx not using RealCNNService');
      if (!details.importsRealCNN) issues.push('processing.tsx not importing RealCNNService');
      if (!details.noEnhancedOffline) issues.push('processing.tsx still using EnhancedOfflineAIService');
      if (!details.noMockAI) issues.push('processing.tsx still using MockAIService');
    }

    // Check _layout.tsx initializes RealCNNService
    const layoutPath = path.join(__dirname, '..', 'app', '_layout.tsx');
    if (!fs.existsSync(layoutPath)) {
      issues.push('_layout.tsx not found');
    } else {
      const layoutContent = fs.readFileSync(layoutPath, 'utf8');
      
      details.initializesRealCNN = layoutContent.includes('RealCNNService.initialize');
      details.importsRealCNNInLayout = layoutContent.includes("import('@/services/realCNNService')");
      details.noEnhancedOfflineInLayout = !layoutContent.includes('EnhancedOfflineAIService');
      
      if (!details.initializesRealCNN) issues.push('_layout.tsx not initializing RealCNNService');
      if (!details.importsRealCNNInLayout) issues.push('_layout.tsx not importing RealCNNService');
      if (!details.noEnhancedOfflineInLayout) issues.push('_layout.tsx still using EnhancedOfflineAIService');
    }

    // Verify no old AI services are referenced (excluding legitimate type imports)
    const filesToCheck = [
      'app/processing.tsx',
      'app/_layout.tsx',
      'services/realCNNService.ts'
    ];

    details.cleanReferences = true;
    for (const file of filesToCheck) {
      const filePath = path.join(__dirname, '..', file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');

        // Check for problematic references (excluding type imports)
        const hasEnhancedOfflineAI = content.includes('EnhancedOfflineAIService');
        const hasMockAIService = content.includes('MockAIService');
        const hasProblematicMockAI = content.includes('mockAI') &&
                                    !content.includes("} from './mockAI'") &&
                                    !content.includes('export type');

        if (hasEnhancedOfflineAI || hasMockAIService || hasProblematicMockAI) {
          issues.push(`${file} still references old AI services`);
          details.cleanReferences = false;
        }
      }
    }

    this.verificationResults.processingPipeline = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };

    console.log(`   Status: ${this.verificationResults.processingPipeline.status}`);
    if (issues.length > 0) {
      issues.forEach(issue => console.log(`   ❌ ${issue}`));
    } else {
      console.log('   ✅ Processing pipeline correctly uses RealCNNService');
    }
  }

  /**
   * 4. End-to-End Flow Verification
   */
  async verifyEndToEndFlow() {
    console.log('\n🔄 4. VERIFYING END-TO-END FLOW...');
    
    const issues = [];
    const details = {};

    // Check camera integration
    const cameraPath = path.join(__dirname, '..', 'app', 'camera.tsx');
    if (fs.existsSync(cameraPath)) {
      const cameraContent = fs.readFileSync(cameraPath, 'utf8');
      details.cameraNavigatesToProcessing = cameraContent.includes("pathname: '/processing'");
      
      if (!details.cameraNavigatesToProcessing) {
        issues.push('Camera not properly navigating to processing');
      }
    }

    // Verify processing flow
    if (fs.existsSync(path.join(__dirname, '..', 'app', 'processing.tsx'))) {
      const processingContent = fs.readFileSync(path.join(__dirname, '..', 'app', 'processing.tsx'), 'utf8');
      
      details.hasImageUpload = processingContent.includes('uploadResult');
      details.hasCNNAnalysis = processingContent.includes('RealCNNService.measureFoot');
      details.hasResultsNavigation = processingContent.includes("pathname: '/results'");
      
      if (!details.hasImageUpload) issues.push('Image upload step missing');
      if (!details.hasCNNAnalysis) issues.push('CNN analysis step missing');
      if (!details.hasResultsNavigation) issues.push('Results navigation missing');
    }

    // Check results display
    const resultsPath = path.join(__dirname, '..', 'app', 'results.tsx');
    if (fs.existsSync(resultsPath)) {
      const resultsContent = fs.readFileSync(resultsPath, 'utf8');
      details.displaysRecommendations = resultsContent.includes('recommendations');
      details.showsMeasurements = resultsContent.includes('foot_length') || resultsContent.includes('footLength');
      
      if (!details.displaysRecommendations) issues.push('Results not displaying recommendations');
      if (!details.showsMeasurements) issues.push('Results not showing measurements');
    }

    // Verify error handling
    details.hasErrorHandling = true;
    const criticalFiles = ['app/processing.tsx', 'services/realCNNService.ts'];
    for (const file of criticalFiles) {
      const filePath = path.join(__dirname, '..', file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        if (!content.includes('try') || !content.includes('catch')) {
          issues.push(`${file} missing proper error handling`);
          details.hasErrorHandling = false;
        }
      }
    }

    this.verificationResults.endToEndFlow = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };

    console.log(`   Status: ${this.verificationResults.endToEndFlow.status}`);
    if (issues.length > 0) {
      issues.forEach(issue => console.log(`   ❌ ${issue}`));
    } else {
      console.log('   ✅ Complete workflow verified (Camera → CNN → Supabase → Results)');
    }
  }

  /**
   * 5. Academic Readiness Verification
   */
  async verifyAcademicReadiness() {
    console.log('\n🎓 5. VERIFYING ACADEMIC READINESS...');
    
    const issues = [];
    const details = {};

    // Check dataset quality
    const datasetPath = path.join(__dirname, '..', 'datasets');
    if (fs.existsSync(datasetPath)) {
      const trainImages = fs.readdirSync(path.join(datasetPath, 'train', 'images')).length;
      details.datasetSize = trainImages;
      details.adequateSize = trainImages >= 1000;
      
      if (!details.adequateSize) {
        issues.push(`Dataset too small for academic standards: ${trainImages} images`);
      }
    } else {
      issues.push('Dataset directory not found');
    }

    // Check documentation
    const docPaths = [
      'README.md',
      'datasets/README.md', 
      'assets/models/README.md'
    ];
    
    details.documentationComplete = true;
    for (const docPath of docPaths) {
      const fullPath = path.join(__dirname, '..', docPath);
      if (!fs.existsSync(fullPath)) {
        issues.push(`Missing documentation: ${docPath}`);
        details.documentationComplete = false;
      }
    }

    // Verify real AI implementation
    details.realAIImplemented = (
      this.verificationResults.cnnIntegration.status === 'PASS' &&
      this.verificationResults.supabaseIntegration.status === 'PASS' &&
      this.verificationResults.processingPipeline.status === 'PASS'
    );

    if (!details.realAIImplemented) {
      issues.push('Real AI implementation not fully verified');
    }

    // Check academic metadata
    const modelJsonPath = path.join(__dirname, '..', 'assets', 'models', 'foot_measurement_model.json');
    if (fs.existsSync(modelJsonPath)) {
      const modelJson = JSON.parse(fs.readFileSync(modelJsonPath, 'utf8'));
      details.hasAcademicMetadata = !!(modelJson.training_info?.academic_project);
      
      if (!details.hasAcademicMetadata) {
        issues.push('Model missing academic project metadata');
      }
    }

    // Performance reliability check
    details.reliableForDemo = (
      this.verificationResults.endToEndFlow.status === 'PASS' &&
      this.verificationResults.endToEndFlow.details.hasErrorHandling
    );

    if (!details.reliableForDemo) {
      issues.push('Implementation may not be reliable for academic demonstration');
    }

    this.verificationResults.academicReadiness = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };

    console.log(`   Status: ${this.verificationResults.academicReadiness.status}`);
    if (issues.length > 0) {
      issues.forEach(issue => console.log(`   ❌ ${issue}`));
    } else {
      console.log('   ✅ Ready for academic demonstration with real AI');
    }
  }

  /**
   * Calculate overall verification status
   */
  calculateOverallStatus() {
    const categories = ['cnnIntegration', 'supabaseIntegration', 'processingPipeline', 'endToEndFlow', 'academicReadiness'];
    const weights = { 
      cnnIntegration: 0.3, 
      supabaseIntegration: 0.25, 
      processingPipeline: 0.2, 
      endToEndFlow: 0.15, 
      academicReadiness: 0.1 
    };
    
    let totalScore = 0;
    let passedCategories = 0;

    for (const category of categories) {
      if (this.verificationResults[category].status === 'PASS') {
        totalScore += weights[category] * 100;
        passedCategories++;
      }
    }

    this.verificationResults.overall = {
      status: passedCategories === categories.length ? 'PASS' : 'FAIL',
      score: Math.round(totalScore),
      readyForDemo: passedCategories === categories.length,
      passedCategories,
      totalCategories: categories.length
    };
  }

  /**
   * Print comprehensive verification results
   */
  printComprehensiveResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 COMPREHENSIVE VERIFICATION RESULTS');
    console.log('='.repeat(60));

    const overall = this.verificationResults.overall;
    console.log(`\n🎯 OVERALL STATUS: ${overall.status}`);
    console.log(`📈 VERIFICATION SCORE: ${overall.score}/100`);
    console.log(`✅ CATEGORIES PASSED: ${overall.passedCategories}/${overall.totalCategories}`);
    console.log(`🎓 READY FOR ACADEMIC DEMO: ${overall.readyForDemo ? 'YES' : 'NO'}`);

    if (overall.readyForDemo) {
      console.log('\n🎉 VERIFICATION SUCCESSFUL!');
      console.log('Your FootFit app is ready for academic demonstration with:');
      console.log('✅ Real CNN using TensorFlow.js with data-informed weights');
      console.log('✅ Pure Supabase integration (no static fallbacks)');
      console.log('✅ Complete processing pipeline using RealCNNService');
      console.log('✅ End-to-end workflow from camera to results');
      console.log('✅ Academic-grade implementation and documentation');
      
      console.log('\n📋 DEMONSTRATION CHECKLIST:');
      console.log('✅ 1,629 real foot images processed into training dataset');
      console.log('✅ Data-informed CNN weights (not placeholder)');
      console.log('✅ TensorFlow.js real computer vision processing');
      console.log('✅ Supabase database integration (no mock data)');
      console.log('✅ Reliable error handling for live demonstrations');
      
    } else {
      console.log('\n⚠️  VERIFICATION ISSUES FOUND:');
      const allIssues = [];
      Object.values(this.verificationResults).forEach(result => {
        if (result.issues) allIssues.push(...result.issues);
      });
      allIssues.forEach(issue => console.log(`❌ ${issue}`));
      
      console.log('\n🔧 Please address these issues before academic demonstration.');
    }

    console.log('\n' + '='.repeat(60));
  }
}

// Run verification if called directly
if (require.main === module) {
  const verifier = new FootFitVerifier();
  verifier.verify().catch(console.error);
}

module.exports = FootFitVerifier;
