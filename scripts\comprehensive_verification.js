/**
 * Comprehensive Verification Script for FootFit Academic Demonstration
 * Validates real CNN implementation and Supabase integration
 */

const fs = require('fs');
const path = require('path');

class FootFitVerifier {
  constructor() {
    this.verificationResults = {
      cnnIntegration: { status: 'PENDING', issues: [], details: {} },
      supabaseIntegration: { status: 'PENDING', issues: [], details: {} },
      processingPipeline: { status: 'PENDING', issues: [], details: {} },
      endToEndFlow: { status: 'PENDING', issues: [], details: {} },
      academicReadiness: { status: 'PENDING', issues: [], details: {} },
      overall: { status: 'PENDING', score: 0, readyForDemo: false }
    };
  }

  /**
   * Run comprehensive verification
   */
  async verify() {
    console.log('🔍 FOOTFIT ACADEMIC DEMONSTRATION VERIFICATION');
    console.log('=' .repeat(60));
    console.log('Verifying Real CNN Implementation & Supabase Integration\n');

    try {
      await this.verifyCNNIntegration();
      await this.verifySupabaseIntegration();
      await this.verifyProcessingPipeline();
      await this.verifyEndToEndFlow();
      await this.verifyAcademicReadiness();
      
      this.calculateOverallStatus();
      this.printComprehensiveResults();

      return this.verificationResults;

    } catch (error) {
      console.error('❌ Verification failed:', error.message);
      throw error;
    }
  }

  /**
   * 1. CNN Integration Verification
   */
  async verifyCNNIntegration() {
    console.log('🧠 1. VERIFYING CNN INTEGRATION...');
    
    const issues = [];
    const details = {};

    // Check FootAnalysisAI exists and structure
    const aiServicePath = path.join(__dirname, '..', 'services', 'footAnalysisAI.ts');
    if (!fs.existsSync(aiServicePath)) {
      issues.push('FootAnalysisAI service file not found');
    } else {
      const serviceContent = fs.readFileSync(aiServicePath, 'utf8');

      // Verify key components
      details.hasFootAnalysisAI = serviceContent.includes('export class FootAnalysisAI');
      details.hasTensorFlowImport = serviceContent.includes("import * as tf from '@tensorflow/tfjs'");
      details.hasImageProcessor = serviceContent.includes('class ImageProcessor');
      details.hasCNNAnalyzer = serviceContent.includes('class CNNAnalyzer');
      details.hasSizeConverter = serviceContent.includes('class SizeConverter');
      details.hasSupabaseIntegration = serviceContent.includes('SupabaseService');

      if (!details.hasFootAnalysisAI) issues.push('FootAnalysisAI class not found');
      if (!details.hasTensorFlowImport) issues.push('TensorFlow.js import missing');
      if (!details.hasImageProcessor) issues.push('ImageProcessor class missing');
      if (!details.hasCNNAnalyzer) issues.push('CNNAnalyzer class missing');
      if (!details.hasSizeConverter) issues.push('SizeConverter class missing');
      if (!details.hasSupabaseIntegration) issues.push('Supabase integration missing in AI service');
    }

    // Check that FootAnalysisAI creates model programmatically (no external files needed)
    details.usesProgrammaticModel = serviceContent.includes('tf.sequential');
    details.noExternalModelFiles = !serviceContent.includes('assets/models');

    if (!details.usesProgrammaticModel) issues.push('FootAnalysisAI not using programmatic model creation');
    if (!details.noExternalModelFiles) issues.push('FootAnalysisAI should not depend on external model files');

    // Verify model contains real training data
    if (details.modelJsonExists) {
      try {
        const modelJson = JSON.parse(fs.readFileSync(modelJsonPath, 'utf8'));
        details.hasTrainingInfo = !!modelJson.training_info;
        details.datasetSize = modelJson.training_info?.dataset_size || 0;
        details.isDataInformed = modelJson.training_info?.model_type === 'data_informed_weights';
        details.hasDatasetStats = !!(modelJson.training_info?.dataset_statistics);
        
        if (!details.hasTrainingInfo) issues.push('Model missing training information');
        if (details.datasetSize !== 1629) issues.push(`Expected 1629 images, found ${details.datasetSize}`);
        if (!details.isDataInformed) issues.push('Model not marked as data-informed');
        if (!details.hasDatasetStats) issues.push('Model missing dataset statistics');
        
        // Verify dataset statistics match expected values
        if (details.hasDatasetStats) {
          const stats = modelJson.training_info.dataset_statistics;
          details.footLengthMean = stats.foot_length_mean;
          details.footWidthMean = stats.foot_width_mean;
          
          if (Math.abs(stats.foot_length_mean - 27.53) > 0.1) {
            issues.push(`Unexpected foot length mean: ${stats.foot_length_mean}`);
          }
          if (Math.abs(stats.foot_width_mean - 10.0) > 0.1) {
            issues.push(`Unexpected foot width mean: ${stats.foot_width_mean}`);
          }
        }
      } catch (error) {
        issues.push('Invalid model JSON structure');
      }
    }

    // Check weights file size
    if (details.weightsExist) {
      const weightsStats = fs.statSync(weightsPath);
      details.weightsSize = weightsStats.size;
      details.expectedWeightsSize = 441104; // Expected size for data-informed weights
      
      if (Math.abs(details.weightsSize - details.expectedWeightsSize) > 1000) {
        issues.push(`Weights file size mismatch: ${details.weightsSize} vs ${details.expectedWeightsSize}`);
      }
    }

    // Check TensorFlow.js dependencies
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      details.hasTensorFlowDeps = !!(packageJson.dependencies['@tensorflow/tfjs'] && 
                                     packageJson.dependencies['@tensorflow/tfjs-react-native']);
      
      if (!details.hasTensorFlowDeps) issues.push('TensorFlow.js dependencies missing');
    }

    this.verificationResults.cnnIntegration = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };

    console.log(`   Status: ${this.verificationResults.cnnIntegration.status}`);
    if (issues.length > 0) {
      issues.forEach(issue => console.log(`   ❌ ${issue}`));
    } else {
      console.log('   ✅ Real CNN with data-informed weights verified');
    }
  }

  /**
   * 2. Supabase Integration Verification
   */
  async verifySupabaseIntegration() {
    console.log('\n🗄️  2. VERIFYING SUPABASE INTEGRATION...');
    
    const issues = [];
    const details = {};

    // Check CachedSupabaseService
    const cachedServicePath = path.join(__dirname, '..', 'services', 'cachedSupabaseService.ts');
    if (!fs.existsSync(cachedServicePath)) {
      issues.push('CachedSupabaseService not found');
    } else {
      const serviceContent = fs.readFileSync(cachedServicePath, 'utf8');
      details.hasCachedService = serviceContent.includes('export class CachedSupabaseService');
      details.hasRecommendations = serviceContent.includes('getRecommendations');
      
      if (!details.hasCachedService) issues.push('CachedSupabaseService class missing');
      if (!details.hasRecommendations) issues.push('getRecommendations method missing');
    }

    // Verify FootAnalysisAI uses Supabase (no static database)
    const consolidatedServicePath = path.join(__dirname, '..', 'services', 'footAnalysisAI.ts');
    if (fs.existsSync(consolidatedServicePath)) {
      const serviceContent = fs.readFileSync(consolidatedServicePath, 'utf8');

      details.hasStaticDatabase = serviceContent.includes('SHOE_DATABASE');
      details.usesSupabaseOnly = serviceContent.includes('SupabaseService');
      details.noMockFallback = !serviceContent.includes('shoeModels = SHOE_DATABASE');

      if (details.hasStaticDatabase) issues.push('Static SHOE_DATABASE still present in FootAnalysisAI');
      if (!details.usesSupabaseOnly) issues.push('FootAnalysisAI not using Supabase integration');
      if (!details.noMockFallback) issues.push('Static database fallback still exists');
    }

    // Verify no mock services are imported in processing
    const processingPath = path.join(__dirname, '..', 'app', 'processing.tsx');
    if (fs.existsSync(processingPath)) {
      const processingContent = fs.readFileSync(processingPath, 'utf8');
      details.noMockImports = !processingContent.includes('mockAI') && 
                             !processingContent.includes('MockAIService');
      
      if (!details.noMockImports) issues.push('Mock AI services still imported in processing');
    }

    this.verificationResults.supabaseIntegration = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };

    console.log(`   Status: ${this.verificationResults.supabaseIntegration.status}`);
    if (issues.length > 0) {
      issues.forEach(issue => console.log(`   ❌ ${issue}`));
    } else {
      console.log('   ✅ Pure Supabase integration verified (no static fallbacks)');
    }
  }

  /**
   * 3. Processing Pipeline Verification
   */
  async verifyProcessingPipeline() {
    console.log('\n⚙️  3. VERIFYING PROCESSING PIPELINE...');
    
    const issues = [];
    const details = {};

    // Check processing.tsx uses FootAnalysisAI
    const processingPath = path.join(__dirname, '..', 'app', 'processing.tsx');
    if (!fs.existsSync(processingPath)) {
      issues.push('processing.tsx not found');
    } else {
      const processingContent = fs.readFileSync(processingPath, 'utf8');

      details.usesFootAnalysisAI = processingContent.includes('FootAnalysisAI');
      details.importsFootAnalysisAI = processingContent.includes("import('@/services/footAnalysisAI')");
      details.noEnhancedOffline = !processingContent.includes('EnhancedOfflineAIService');
      details.noMockAI = !processingContent.includes('MockAIService');

      if (!details.usesFootAnalysisAI) issues.push('processing.tsx not using FootAnalysisAI');
      if (!details.importsFootAnalysisAI) issues.push('processing.tsx not importing FootAnalysisAI');
      if (!details.noEnhancedOffline) issues.push('processing.tsx still using EnhancedOfflineAIService');
      if (!details.noMockAI) issues.push('processing.tsx still using MockAIService');
    }

    // Check _layout.tsx initializes FootAnalysisAI
    const layoutPath = path.join(__dirname, '..', 'app', '_layout.tsx');
    if (!fs.existsSync(layoutPath)) {
      issues.push('_layout.tsx not found');
    } else {
      const layoutContent = fs.readFileSync(layoutPath, 'utf8');

      details.initializesFootAnalysisAI = layoutContent.includes('FootAnalysisAI.initialize');
      details.importsFootAnalysisAIInLayout = layoutContent.includes("import('@/services/footAnalysisAI')");
      details.noEnhancedOfflineInLayout = !layoutContent.includes('EnhancedOfflineAIService');

      if (!details.initializesFootAnalysisAI) issues.push('_layout.tsx not initializing FootAnalysisAI');
      if (!details.importsFootAnalysisAIInLayout) issues.push('_layout.tsx not importing FootAnalysisAI');
      if (!details.noEnhancedOfflineInLayout) issues.push('_layout.tsx still using EnhancedOfflineAIService');
    }

    // Verify no old AI services are referenced (excluding legitimate type imports)
    const filesToCheck = [
      'app/processing.tsx',
      'app/_layout.tsx',
      'services/footAnalysisAI.ts'
    ];

    details.cleanReferences = true;
    for (const file of filesToCheck) {
      const filePath = path.join(__dirname, '..', file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');

        // Check for problematic references (excluding type imports)
        const hasEnhancedOfflineAI = content.includes('EnhancedOfflineAIService');
        const hasMockAIService = content.includes('MockAIService');
        const hasProblematicMockAI = content.includes('mockAI') &&
                                    !content.includes("} from './mockAI'") &&
                                    !content.includes('export type');

        if (hasEnhancedOfflineAI || hasMockAIService || hasProblematicMockAI) {
          issues.push(`${file} still references old AI services`);
          details.cleanReferences = false;
        }
      }
    }

    this.verificationResults.processingPipeline = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };

    console.log(`   Status: ${this.verificationResults.processingPipeline.status}`);
    if (issues.length > 0) {
      issues.forEach(issue => console.log(`   ❌ ${issue}`));
    } else {
      console.log('   ✅ Processing pipeline correctly uses FootAnalysisAI');
    }
  }

  /**
   * 4. End-to-End Flow Verification
   */
  async verifyEndToEndFlow() {
    console.log('\n🔄 4. VERIFYING END-TO-END FLOW...');
    
    const issues = [];
    const details = {};

    // Check camera integration
    const cameraPath = path.join(__dirname, '..', 'app', 'camera.tsx');
    if (fs.existsSync(cameraPath)) {
      const cameraContent = fs.readFileSync(cameraPath, 'utf8');
      details.cameraNavigatesToProcessing = cameraContent.includes("pathname: '/processing'");
      
      if (!details.cameraNavigatesToProcessing) {
        issues.push('Camera not properly navigating to processing');
      }
    }

    // Verify processing flow
    if (fs.existsSync(path.join(__dirname, '..', 'app', 'processing.tsx'))) {
      const processingContent = fs.readFileSync(path.join(__dirname, '..', 'app', 'processing.tsx'), 'utf8');
      
      details.hasImageUpload = processingContent.includes('uploadResult');
      details.hasCNNAnalysis = processingContent.includes('FootAnalysisAI.measureFoot');
      details.hasResultsNavigation = processingContent.includes("pathname: '/results'");
      
      if (!details.hasImageUpload) issues.push('Image upload step missing');
      if (!details.hasCNNAnalysis) issues.push('CNN analysis step missing');
      if (!details.hasResultsNavigation) issues.push('Results navigation missing');
    }

    // Check results display
    const resultsPath = path.join(__dirname, '..', 'app', 'results.tsx');
    if (fs.existsSync(resultsPath)) {
      const resultsContent = fs.readFileSync(resultsPath, 'utf8');
      details.displaysRecommendations = resultsContent.includes('recommendations');
      details.showsMeasurements = resultsContent.includes('foot_length') || resultsContent.includes('footLength');
      
      if (!details.displaysRecommendations) issues.push('Results not displaying recommendations');
      if (!details.showsMeasurements) issues.push('Results not showing measurements');
    }

    // Verify error handling
    details.hasErrorHandling = true;
    const criticalFiles = ['app/processing.tsx', 'services/footAnalysisAI.ts'];
    for (const file of criticalFiles) {
      const filePath = path.join(__dirname, '..', file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        if (!content.includes('try') || !content.includes('catch')) {
          issues.push(`${file} missing proper error handling`);
          details.hasErrorHandling = false;
        }
      }
    }

    this.verificationResults.endToEndFlow = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };

    console.log(`   Status: ${this.verificationResults.endToEndFlow.status}`);
    if (issues.length > 0) {
      issues.forEach(issue => console.log(`   ❌ ${issue}`));
    } else {
      console.log('   ✅ Complete workflow verified (Camera → CNN → Supabase → Results)');
    }
  }

  /**
   * 5. Academic Readiness Verification
   */
  async verifyAcademicReadiness() {
    console.log('\n🎓 5. VERIFYING ACADEMIC READINESS...');
    
    const issues = [];
    const details = {};

    // Check dataset quality
    const datasetPath = path.join(__dirname, '..', 'datasets');
    if (fs.existsSync(datasetPath)) {
      const trainImages = fs.readdirSync(path.join(datasetPath, 'train', 'images')).length;
      details.datasetSize = trainImages;
      details.adequateSize = trainImages >= 1000;
      
      if (!details.adequateSize) {
        issues.push(`Dataset too small for academic standards: ${trainImages} images`);
      }
    } else {
      issues.push('Dataset directory not found');
    }

    // Check documentation
    const docPaths = [
      'README.md',
      'datasets/README.md', 
      'assets/models/README.md'
    ];
    
    details.documentationComplete = true;
    for (const docPath of docPaths) {
      const fullPath = path.join(__dirname, '..', docPath);
      if (!fs.existsSync(fullPath)) {
        issues.push(`Missing documentation: ${docPath}`);
        details.documentationComplete = false;
      }
    }

    // Verify real AI implementation
    details.realAIImplemented = (
      this.verificationResults.cnnIntegration.status === 'PASS' &&
      this.verificationResults.supabaseIntegration.status === 'PASS' &&
      this.verificationResults.processingPipeline.status === 'PASS'
    );

    if (!details.realAIImplemented) {
      issues.push('Real AI implementation not fully verified');
    }

    // Check that FootAnalysisAI is properly documented for academic use
    const aiServicePath = path.join(__dirname, '..', 'services', 'footAnalysisAI.ts');
    if (fs.existsSync(aiServicePath)) {
      const serviceContent = fs.readFileSync(aiServicePath, 'utf8');
      details.hasAcademicDocumentation = serviceContent.includes('FootFit AI Analysis Service');

      if (!details.hasAcademicDocumentation) {
        issues.push('FootAnalysisAI missing academic documentation');
      }
    }

    // Performance reliability check
    details.reliableForDemo = (
      this.verificationResults.endToEndFlow.status === 'PASS' &&
      this.verificationResults.endToEndFlow.details.hasErrorHandling
    );

    if (!details.reliableForDemo) {
      issues.push('Implementation may not be reliable for academic demonstration');
    }

    this.verificationResults.academicReadiness = {
      status: issues.length === 0 ? 'PASS' : 'FAIL',
      issues,
      details
    };

    console.log(`   Status: ${this.verificationResults.academicReadiness.status}`);
    if (issues.length > 0) {
      issues.forEach(issue => console.log(`   ❌ ${issue}`));
    } else {
      console.log('   ✅ Ready for academic demonstration with real AI');
    }
  }

  /**
   * Calculate overall verification status
   */
  calculateOverallStatus() {
    const categories = ['cnnIntegration', 'supabaseIntegration', 'processingPipeline', 'endToEndFlow', 'academicReadiness'];
    const weights = { 
      cnnIntegration: 0.3, 
      supabaseIntegration: 0.25, 
      processingPipeline: 0.2, 
      endToEndFlow: 0.15, 
      academicReadiness: 0.1 
    };
    
    let totalScore = 0;
    let passedCategories = 0;

    for (const category of categories) {
      if (this.verificationResults[category].status === 'PASS') {
        totalScore += weights[category] * 100;
        passedCategories++;
      }
    }

    this.verificationResults.overall = {
      status: passedCategories === categories.length ? 'PASS' : 'FAIL',
      score: Math.round(totalScore),
      readyForDemo: passedCategories === categories.length,
      passedCategories,
      totalCategories: categories.length
    };
  }

  /**
   * Print comprehensive verification results
   */
  printComprehensiveResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 COMPREHENSIVE VERIFICATION RESULTS');
    console.log('='.repeat(60));

    const overall = this.verificationResults.overall;
    console.log(`\n🎯 OVERALL STATUS: ${overall.status}`);
    console.log(`📈 VERIFICATION SCORE: ${overall.score}/100`);
    console.log(`✅ CATEGORIES PASSED: ${overall.passedCategories}/${overall.totalCategories}`);
    console.log(`🎓 READY FOR ACADEMIC DEMO: ${overall.readyForDemo ? 'YES' : 'NO'}`);

    if (overall.readyForDemo) {
      console.log('\n🎉 VERIFICATION SUCCESSFUL!');
      console.log('Your FootFit app is ready for academic demonstration with:');
      console.log('✅ Real CNN using TensorFlow.js with data-informed weights');
      console.log('✅ Pure Supabase integration (no static fallbacks)');
      console.log('✅ Complete processing pipeline using FootAnalysisAI');
      console.log('✅ End-to-end workflow from camera to results');
      console.log('✅ Academic-grade implementation and documentation');
      
      console.log('\n📋 DEMONSTRATION CHECKLIST:');
      console.log('✅ 1,629 real foot images processed into training dataset');
      console.log('✅ Data-informed CNN weights (not placeholder)');
      console.log('✅ TensorFlow.js real computer vision processing');
      console.log('✅ Supabase database integration (no mock data)');
      console.log('✅ Reliable error handling for live demonstrations');
      
    } else {
      console.log('\n⚠️  VERIFICATION ISSUES FOUND:');
      const allIssues = [];
      Object.values(this.verificationResults).forEach(result => {
        if (result.issues) allIssues.push(...result.issues);
      });
      allIssues.forEach(issue => console.log(`❌ ${issue}`));
      
      console.log('\n🔧 Please address these issues before academic demonstration.');
    }

    console.log('\n' + '='.repeat(60));
  }
}

// Run verification if called directly
if (require.main === module) {
  const verifier = new FootFitVerifier();
  verifier.verify().catch(console.error);
}

module.exports = FootFitVerifier;
