/**
 * End-to-End Testing Script for FootFit Real CNN Implementation
 * Tests complete pipeline from dataset through CNN to Supabase recommendations
 */

const fs = require('fs');
const path = require('path');

class EndToEndTester {
  constructor() {
    this.testResults = {
      dataset: { passed: false, details: {} },
      model: { passed: false, details: {} },
      integration: { passed: false, details: {} },
      academic: { passed: false, details: {} },
      overall: { passed: false, score: 0 }
    };
  }

  /**
   * Run complete end-to-end testing
   */
  async test() {
    console.log('🧪 Starting FootFit End-to-End Testing...');
    console.log('Testing Real CNN Implementation with Actual Dataset');

    try {
      await this.testDatasetIntegration();
      await this.testModelIntegration();
      await this.testCNNService();
      await this.testAcademicReadiness();
      
      this.calculateOverallScore();
      this.printResults();

      return this.testResults;

    } catch (error) {
      console.error('❌ End-to-end testing failed:', error.message);
      throw error;
    }
  }

  /**
   * Test dataset integration and quality
   */
  async testDatasetIntegration() {
    console.log('📊 Testing dataset integration...');

    const datasetPath = path.join(__dirname, '..', 'datasets');
    const details = {};

    // Test 1: Directory structure
    const requiredDirs = ['train/images', 'train/annotations', 'validation/images', 'validation/annotations', 'test/images', 'test/annotations'];
    let structureValid = true;
    
    for (const dir of requiredDirs) {
      const dirPath = path.join(datasetPath, dir);
      if (!fs.existsSync(dirPath)) {
        structureValid = false;
        break;
      }
    }
    details.structure = structureValid;

    // Test 2: Dataset size
    const trainImages = fs.readdirSync(path.join(datasetPath, 'train', 'images')).length;
    const valImages = fs.readdirSync(path.join(datasetPath, 'validation', 'images')).length;
    const testImages = fs.readdirSync(path.join(datasetPath, 'test', 'images')).length;
    const totalImages = trainImages + valImages + testImages;
    
    details.totalImages = totalImages;
    details.splits = { train: trainImages, validation: valImages, test: testImages };
    details.sizeAdequate = totalImages >= 1000; // Academic standard

    // Test 3: Annotation quality
    const sampleAnnotations = this.testAnnotationQuality(datasetPath);
    details.annotations = sampleAnnotations;

    // Test 4: Metadata completeness
    const metadataPath = path.join(datasetPath, 'metadata', 'dataset_info.json');
    details.metadata = fs.existsSync(metadataPath);

    this.testResults.dataset.details = details;
    this.testResults.dataset.passed = structureValid && details.sizeAdequate && details.metadata && sampleAnnotations.valid;

    console.log(`Dataset: ${this.testResults.dataset.passed ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Images: ${totalImages} (Train: ${trainImages}, Val: ${valImages}, Test: ${testImages})`);
  }

  /**
   * Test model integration and weights
   */
  async testModelIntegration() {
    console.log('🧠 Testing model integration...');

    const modelPath = path.join(__dirname, '..', 'assets', 'models');
    const details = {};

    // Test 1: Model files exist
    const modelJsonPath = path.join(modelPath, 'foot_measurement_model.json');
    const weightsPath = path.join(modelPath, 'foot_measurement_model_weights.bin');
    
    details.modelJsonExists = fs.existsSync(modelJsonPath);
    details.weightsExist = fs.existsSync(weightsPath);

    // Test 2: Model structure validation
    if (details.modelJsonExists) {
      try {
        const modelJson = JSON.parse(fs.readFileSync(modelJsonPath, 'utf8'));
        details.validStructure = !!(modelJson.modelTopology && modelJson.weightsManifest);
        details.hasTrainingInfo = !!modelJson.training_info;
        details.datasetSize = modelJson.training_info?.dataset_size || 0;
        details.trainingDate = modelJson.training_info?.training_date;
      } catch (error) {
        details.validStructure = false;
        details.hasTrainingInfo = false;
      }
    }

    // Test 3: Weights file validation
    if (details.weightsExist) {
      const weightsStats = fs.statSync(weightsPath);
      details.weightsSize = weightsStats.size;
      details.expectedSize = 110276 * 4; // Expected size for our model
      details.weightsSizeCorrect = Math.abs(details.weightsSize - details.expectedSize) < 1000;
    }

    this.testResults.model.details = details;
    this.testResults.model.passed = details.modelJsonExists && details.weightsExist && 
                                   details.validStructure && details.hasTrainingInfo && 
                                   details.weightsSizeCorrect;

    console.log(`Model: ${this.testResults.model.passed ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Dataset Size: ${details.datasetSize}`);
    console.log(`  Training Date: ${details.trainingDate}`);
  }

  /**
   * Test CNN service integration
   */
  async testCNNService() {
    console.log('🔬 Testing CNN service integration...');

    const details = {};

    // Test 1: Service file exists
    const cnnServicePath = path.join(__dirname, '..', 'services', 'realCNNService.ts');
    details.serviceExists = fs.existsSync(cnnServicePath);

    // Test 2: Check service structure
    if (details.serviceExists) {
      const serviceContent = fs.readFileSync(cnnServicePath, 'utf8');
      details.hasRealCNNService = serviceContent.includes('class RealCNNService');
      details.hasTensorFlowImport = serviceContent.includes('@tensorflow/tfjs');
      details.hasImageProcessing = serviceContent.includes('CNNImageProcessor');
      details.hasSupabaseIntegration = serviceContent.includes('CachedSupabaseService');
    }

    // Test 3: Check processing pipeline integration
    const processingPath = path.join(__dirname, '..', 'app', 'processing.tsx');
    if (fs.existsSync(processingPath)) {
      const processingContent = fs.readFileSync(processingPath, 'utf8');
      details.processingUsesRealCNN = processingContent.includes('RealCNNService');
      details.removedEnhancedOffline = !processingContent.includes('EnhancedOfflineAIService');
    }

    // Test 4: Check app layout integration
    const layoutPath = path.join(__dirname, '..', 'app', '_layout.tsx');
    if (fs.existsSync(layoutPath)) {
      const layoutContent = fs.readFileSync(layoutPath, 'utf8');
      details.layoutUsesRealCNN = layoutContent.includes('RealCNNService');
    }

    this.testResults.integration.details = details;
    this.testResults.integration.passed = details.serviceExists && details.hasRealCNNService && 
                                         details.hasTensorFlowImport && details.processingUsesRealCNN;

    console.log(`Integration: ${this.testResults.integration.passed ? '✅ PASS' : '❌ FAIL'}`);
  }

  /**
   * Test academic readiness
   */
  async testAcademicReadiness() {
    console.log('🎓 Testing academic readiness...');

    const details = {};

    // Test 1: Real AI implementation
    details.realAI = this.testResults.model.passed && this.testResults.integration.passed;

    // Test 2: Dataset size for academic standards
    details.adequateDataset = this.testResults.dataset.details.totalImages >= 1000;

    // Test 3: No static fallbacks
    const aiServicePath = path.join(__dirname, '..', 'services', 'aiService.ts');
    if (fs.existsSync(aiServicePath)) {
      const aiServiceContent = fs.readFileSync(aiServicePath, 'utf8');
      details.noStaticDatabase = !aiServiceContent.includes('SHOE_DATABASE');
      details.pureSupabaseIntegration = aiServiceContent.includes('CachedSupabaseService');
    }

    // Test 4: Documentation quality
    const readmePaths = [
      path.join(__dirname, '..', 'README.md'),
      path.join(__dirname, '..', 'datasets', 'README.md'),
      path.join(__dirname, '..', 'assets', 'models', 'README.md')
    ];
    details.documentationComplete = readmePaths.every(p => fs.existsSync(p));

    // Test 5: Academic metadata
    const modelJsonPath = path.join(__dirname, '..', 'assets', 'models', 'foot_measurement_model.json');
    if (fs.existsSync(modelJsonPath)) {
      const modelJson = JSON.parse(fs.readFileSync(modelJsonPath, 'utf8'));
      details.academicMetadata = !!(modelJson.training_info?.academic_project);
    }

    this.testResults.academic.details = details;
    this.testResults.academic.passed = details.realAI && details.adequateDataset && 
                                      details.noStaticDatabase && details.documentationComplete;

    console.log(`Academic: ${this.testResults.academic.passed ? '✅ PASS' : '❌ FAIL'}`);
  }

  /**
   * Test annotation quality on sample
   */
  testAnnotationQuality(datasetPath) {
    const annotationsPath = path.join(datasetPath, 'train', 'annotations');
    const annotationFiles = fs.readdirSync(annotationsPath).slice(0, 10); // Sample 10 files
    
    let validCount = 0;
    let totalCount = 0;

    for (const file of annotationFiles) {
      try {
        const annotation = JSON.parse(fs.readFileSync(path.join(annotationsPath, file), 'utf8'));
        totalCount++;
        
        if (annotation.measurements && 
            annotation.measurements.foot_length_cm > 15 && 
            annotation.measurements.foot_length_cm < 35 &&
            annotation.measurements.foot_width_cm > 6 && 
            annotation.measurements.foot_width_cm < 15) {
          validCount++;
        }
      } catch (error) {
        totalCount++;
      }
    }

    return {
      valid: validCount === totalCount && totalCount > 0,
      validCount,
      totalCount,
      percentage: totalCount > 0 ? (validCount / totalCount * 100).toFixed(1) : 0
    };
  }

  /**
   * Calculate overall score
   */
  calculateOverallScore() {
    const categories = ['dataset', 'model', 'integration', 'academic'];
    const weights = { dataset: 0.3, model: 0.3, integration: 0.25, academic: 0.15 };
    
    let totalScore = 0;
    let passedCategories = 0;

    for (const category of categories) {
      if (this.testResults[category].passed) {
        totalScore += weights[category] * 100;
        passedCategories++;
      }
    }

    this.testResults.overall.score = Math.round(totalScore);
    this.testResults.overall.passed = passedCategories === categories.length;
  }

  /**
   * Print comprehensive test results
   */
  printResults() {
    console.log('\n📊 End-to-End Test Results:');
    console.log('='.repeat(60));

    // Dataset Results
    console.log('\n📊 DATASET INTEGRATION:');
    const dataset = this.testResults.dataset;
    console.log(`Status: ${dataset.passed ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Total Images: ${dataset.details.totalImages}`);
    console.log(`Train/Val/Test: ${dataset.details.splits.train}/${dataset.details.splits.validation}/${dataset.details.splits.test}`);
    console.log(`Annotations: ${dataset.details.annotations.percentage}% valid`);

    // Model Results
    console.log('\n🧠 MODEL INTEGRATION:');
    const model = this.testResults.model;
    console.log(`Status: ${model.passed ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Dataset Size: ${model.details.datasetSize} images`);
    console.log(`Training Date: ${model.details.trainingDate}`);
    console.log(`Weights Size: ${(model.details.weightsSize / 1024).toFixed(1)}KB`);

    // Integration Results
    console.log('\n🔬 CNN SERVICE INTEGRATION:');
    const integration = this.testResults.integration;
    console.log(`Status: ${integration.passed ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Real CNN Service: ${integration.details.hasRealCNNService ? '✅' : '❌'}`);
    console.log(`TensorFlow.js: ${integration.details.hasTensorFlowImport ? '✅' : '❌'}`);
    console.log(`Processing Pipeline: ${integration.details.processingUsesRealCNN ? '✅' : '❌'}`);

    // Academic Results
    console.log('\n🎓 ACADEMIC READINESS:');
    const academic = this.testResults.academic;
    console.log(`Status: ${academic.passed ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Real AI Implementation: ${academic.details.realAI ? '✅' : '❌'}`);
    console.log(`No Static Fallbacks: ${academic.details.noStaticDatabase ? '✅' : '❌'}`);
    console.log(`Pure Supabase Integration: ${academic.details.pureSupabaseIntegration ? '✅' : '❌'}`);

    // Overall Results
    console.log('\n' + '='.repeat(60));
    console.log(`OVERALL SCORE: ${this.testResults.overall.score}/100`);
    console.log(`STATUS: ${this.testResults.overall.passed ? '✅ READY FOR ACADEMIC DEMONSTRATION' : '❌ NEEDS ATTENTION'}`);

    if (this.testResults.overall.passed) {
      console.log('\n🎉 SUCCESS! FootFit is ready for academic demonstration with:');
      console.log('✅ Real CNN implementation using actual foot image datasets');
      console.log('✅ Data-informed model weights replacing mathematical simulation');
      console.log('✅ Pure Supabase integration without static fallbacks');
      console.log('✅ Academic-grade documentation and metadata');
      console.log('✅ Reliable performance for supervisor presentations');
    } else {
      console.log('\n⚠️  Some issues need attention before academic demonstration.');
    }
  }
}

// Run testing if called directly
if (require.main === module) {
  const tester = new EndToEndTester();
  tester.test().catch(console.error);
}

module.exports = EndToEndTester;
