/**
 * Environment variable loader for Expo
 * Ensures environment variables are available across all platforms
 */

// Environment variables for academic demonstration
const ENV_CONFIG = {
  EXPO_PUBLIC_SUPABASE_URL: 'https://ajyfydsivhmpqjknqwzv.supabase.co',
  EXPO_PUBLIC_SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqeWZ5ZHNpdmhtcHFqa25xd3p2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTIxODE4NSwiZXhwIjoyMDY2Nzk0MTg1fQ.B5kTK-PmGnbAi-IS2_BY1SkJMXWkXZ-GsEffTF1xkl4',
  EXPO_PUBLIC_AI_API_URL: 'http://192.168.0.10:5000',
  EXPO_PUBLIC_AI_API_KEY: 'dev-key-footfit-2024',
  EXPO_PUBLIC_SENTRY_DSN: 'https://<EMAIL>/4509625935724544',
};

/**
 * Load environment variables with fallback support
 */
export function loadEnvironmentVariables() {
  // Ensure environment variables are available
  Object.entries(ENV_CONFIG).forEach(([key, value]) => {
    if (!process.env[key]) {
      // @ts-ignore - Setting process.env dynamically
      process.env[key] = value;
    }
  });
}

/**
 * Get environment variable with fallback
 */
export function getEnvVar(key: keyof typeof ENV_CONFIG): string {
  return process.env[key] || ENV_CONFIG[key];
}

// Load environment variables immediately
loadEnvironmentVariables();
